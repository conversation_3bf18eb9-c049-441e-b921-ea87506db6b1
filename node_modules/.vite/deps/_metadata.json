{"hash": "7243b7dc", "configHash": "bd51b11e", "lockfileHash": "da37ff34", "browserHash": "e052c0ab", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3d2f400e", "needsInterop": true}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "edc<PERSON>eef", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "d61f46f6", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "888d457b", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "50b98f8c", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2f756dbb", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "b20bf985", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "751e5bf2", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "0cf2984b", "needsInterop": false}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "6ac23bf0", "needsInterop": false}, "i18next-browser-languagedetector": {"src": "../../i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js", "file": "i18next-browser-languagedetector.js", "fileHash": "cc1e3dc1", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "1fb7162c", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "034a179a", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c0e6fc66", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b8768b8d", "needsInterop": true}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "1158a07a", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "6d421d6a", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "1066f6b9", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "d4f1905a", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "7cce4164", "needsInterop": false}}, "chunks": {"chunk-SKOU5HZS": {"file": "chunk-SKOU5HZS.js"}, "chunk-6SPNF6KQ": {"file": "chunk-6SPNF6KQ.js"}, "chunk-F72CHCPG": {"file": "chunk-F72CHCPG.js"}, "chunk-IENRSBQR": {"file": "chunk-IENRSBQR.js"}, "chunk-R6S4VRB5": {"file": "chunk-R6S4VRB5.js"}, "chunk-4WIT4MX7": {"file": "chunk-4WIT4MX7.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}